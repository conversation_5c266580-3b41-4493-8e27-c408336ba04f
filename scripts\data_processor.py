#!/usr/bin/env python3
"""
内涝数据处理脚本
处理SHP文件和CSV文件，生成用于可视化的数据库和GeoJSON文件
"""

import pandas as pd
import geopandas as gpd
import sqlite3
import json
import os
import re
from pathlib import Path
import numpy as np
from datetime import datetime, timedelta

class FloodDataProcessor:
    def __init__(self, shp_path, csv_path, output_dir):
        """
        初始化数据处理器
        
        Args:
            shp_path: SHP文件路径
            csv_path: CSV文件路径  
            output_dir: 输出目录
        """
        self.shp_path = shp_path
        self.csv_path = csv_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 数据库路径
        self.db_path = self.output_dir / "flood_data.db"
        
    def load_data(self):
        """加载原始数据"""
        print("正在加载数据...")
        
        # 加载SHP文件
        try:
            self.gdf = gpd.read_file(self.shp_path)
            print(f"成功加载SHP文件，包含 {len(self.gdf)} 个网格")
        except Exception as e:
            raise Exception(f"加载SHP文件失败: {e}")
            
        # 加载CSV文件
        try:
            self.df = pd.read_csv(self.csv_path)
            print(f"成功加载CSV文件，包含 {len(self.df)} 条记录")
        except Exception as e:
            raise Exception(f"加载CSV文件失败: {e}")
            
        # 验证数据结构
        self._validate_data()
        
    def _validate_data(self):
        """验证数据结构"""
        required_csv_columns = ['cell_name', 'time_series', 'cell_depth']
        missing_columns = [col for col in required_csv_columns if col not in self.df.columns]
        
        if missing_columns:
            raise Exception(f"CSV文件缺少必要字段: {missing_columns}")
            
        # 检查SHP文件的字段名并统一为cell_name
        if 'JUNNAME' in self.gdf.columns:
            print("发现JUNNAME字段，将其重命名为cell_name")
            self.gdf['cell_name'] = self.gdf['JUNNAME']
        elif 'cell_name' not in self.gdf.columns:
            print("SHP文件中未找到cell_name或JUNNAME字段，将使用索引作为cell_name")
            self.gdf['cell_name'] = self.gdf.index.astype(str)
            
        print("数据验证通过")

    def _check_and_convert_crs(self):
        """检查并转换坐标系"""
        print("正在检查坐标系...")

        # 获取当前坐标系
        current_crs = self.gdf.crs
        print(f"当前坐标系: {current_crs}")

        # 检查是否为地理坐标系
        if current_crs is None:
            print("警告: 未检测到坐标系信息，使用EPSG:4545")
            # 直接设置为EPSG:4545并转换
            self._convert_from_epsg4545()
        elif current_crs.is_geographic:
            print("已经是地理坐标系，无需转换")
            return
        else:
            print("检测到投影坐标系，需要转换为地理坐标系")
            # 转换为WGS84地理坐标系
            try:
                print("正在转换坐标系到WGS84...")
                self.gdf = self.gdf.to_crs('EPSG:4326')
                print("坐标系转换完成")

                # 验证转换结果
                self._validate_geographic_coordinates()

            except Exception as e:
                print(f"坐标系转换失败: {e}")
                print("尝试使用EPSG:4545转换...")
                self._convert_from_epsg4545()

    def _convert_from_epsg4545(self):
        """从EPSG:4545坐标系转换"""
        try:
            print("设置坐标系为EPSG:4545 (CGCS2000 / 3-degree Gauss-Kruger CM 114E)")
            self.gdf.crs = 'EPSG:4545'
            print("正在转换到WGS84...")
            self.gdf = self.gdf.to_crs('EPSG:4326')
            print("EPSG:4545转换成功")

            # 验证转换结果
            self._validate_geographic_coordinates()

        except Exception as e:
            print(f"EPSG:4545转换失败: {e}")
            print("尝试其他坐标系...")
            self._detect_and_set_crs()

    def _detect_and_set_crs(self):
        """检测并设置坐标系"""
        # 获取第一个几何体的坐标范围
        if len(self.gdf) > 0:
            bounds = self.gdf.total_bounds
            min_x, min_y, max_x, max_y = bounds

            print(f"坐标范围: X[{min_x:.2f}, {max_x:.2f}], Y[{min_y:.2f}, {max_y:.2f}]")

            # 首先尝试用户指定的EPSG:4545坐标系
            print("使用指定的EPSG:4545坐标系 (CGCS2000 / 3-degree Gauss-Kruger CM 114E)")
            try:
                self.gdf.crs = 'EPSG:4545'
                self.gdf = self.gdf.to_crs('EPSG:4326')
                print("成功转换EPSG:4545到WGS84")
                self._validate_geographic_coordinates()
                return
            except Exception as e:
                print(f"EPSG:4545转换失败: {e}")

            # 如果EPSG:4545失败，尝试其他可能的坐标系
            print("尝试其他可能的坐标系...")

            # 根据坐标范围判断可能的坐标系
            if 500000 <= min_x <= 800000 and 3000000 <= min_y <= 4000000:
                # 可能是UTM Zone 50N (EPSG:32650) - 中国东部常用
                print("检测到可能的UTM Zone 50N坐标系")
                try:
                    self.gdf.crs = 'EPSG:32650'
                    self.gdf = self.gdf.to_crs('EPSG:4326')
                    print("成功转换UTM Zone 50N到WGS84")
                    self._validate_geographic_coordinates()
                    return
                except Exception as e:
                    print(f"UTM Zone 50N转换失败: {e}")

            # 尝试其他常见的中国坐标系
            china_crs_list = [
                ('CGCS2000 / 3-degree Gauss-Kruger CM 117E', 'EPSG:4548'),
                ('CGCS2000 / 3-degree Gauss-Kruger CM 120E', 'EPSG:4549'),
                ('CGCS2000 / 3-degree Gauss-Kruger CM 123E', 'EPSG:4550'),
                ('CGCS2000 / 3-degree Gauss-Kruger CM 111E', 'EPSG:4546'),
                ('Beijing 1954 / 3-degree Gauss-Kruger CM 117E', 'EPSG:2435'),
                ('Beijing 1954 / 3-degree Gauss-Kruger CM 120E', 'EPSG:2436'),
            ]

            for crs_name, crs_code in china_crs_list:
                try:
                    print(f"尝试 {crs_name}...")
                    self.gdf.crs = crs_code
                    self.gdf = self.gdf.to_crs('EPSG:4326')
                    self._validate_geographic_coordinates()
                    print(f"成功使用 {crs_name} 转换")
                    return
                except Exception as e:
                    print(f"{crs_name} 转换失败: {e}")
                    continue

            print("警告: 无法自动检测坐标系，将使用原始坐标")

    def _validate_geographic_coordinates(self):
        """验证地理坐标的有效性"""
        bounds = self.gdf.total_bounds
        min_x, min_y, max_x, max_y = bounds

        # 检查是否在有效的地理坐标范围内
        if not (-180 <= min_x <= 180 and -180 <= max_x <= 180):
            raise ValueError(f"经度超出有效范围: [{min_x:.6f}, {max_x:.6f}]")

        if not (-90 <= min_y <= 90 and -90 <= max_y <= 90):
            raise ValueError(f"纬度超出有效范围: [{min_y:.6f}, {max_y:.6f}]")

        print(f"地理坐标验证通过: 经度[{min_x:.6f}, {max_x:.6f}], 纬度[{min_y:.6f}, {max_y:.6f}]")

    def _sanitize_filename(self, filename):
        """清理文件名，移除或替换不合法字符"""
        # 替换Windows文件名中的非法字符
        illegal_chars = r'[<>:"/\\|?*]'
        # 将非法字符替换为下划线
        sanitized = re.sub(illegal_chars, '_', str(filename))
        # 移除多余的空格并替换为下划线
        sanitized = re.sub(r'\s+', '_', sanitized)
        # 移除开头和结尾的点和空格
        sanitized = sanitized.strip('. ')
        return sanitized

    def process_time_series(self):
        """处理时间序列数据"""
        print("正在处理时间序列数据...")
        
        # 解析时间序列
        unique_times = sorted(self.df['time_series'].unique())
        self.time_points = unique_times
        
        print(f"发现 {len(unique_times)} 个时间点: {unique_times}")
        
        # 创建时间-水深矩阵
        self.depth_matrix = self.df.pivot(
            index='cell_name', 
            columns='time_series', 
            values='cell_depth'
        ).fillna(0)
        
        print("时间序列数据处理完成")
        
    def create_database(self):
        """创建SQLite数据库"""
        print("正在创建数据库...")

        # 如果数据库文件已存在，删除它以确保干净的开始
        if self.db_path.exists():
            print("删除现有数据库文件...")
            self.db_path.unlink()

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建网格几何表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS grid_geometry (
                cell_name TEXT PRIMARY KEY,
                geometry TEXT,
                centroid_x REAL,
                centroid_y REAL,
                area REAL
            )
        ''')
        
        # 创建水深数据表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS flood_depth (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cell_name TEXT,
                time_series TEXT,
                depth REAL,
                FOREIGN KEY (cell_name) REFERENCES grid_geometry (cell_name)
            )
        ''')
        
        # 创建时间点表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS time_points (
                time_series TEXT PRIMARY KEY,
                sequence_order INTEGER
            )
        ''')
        
        # 插入网格几何数据
        for idx, row in self.gdf.iterrows():
            geom_wkt = row.geometry.wkt
            centroid = row.geometry.centroid
            area = row.geometry.area
            
            cursor.execute('''
                INSERT OR REPLACE INTO grid_geometry 
                (cell_name, geometry, centroid_x, centroid_y, area)
                VALUES (?, ?, ?, ?, ?)
            ''', (row['cell_name'], geom_wkt, centroid.x, centroid.y, area))
        
        # 插入水深数据
        for idx, row in self.df.iterrows():
            cursor.execute('''
                INSERT INTO flood_depth (cell_name, time_series, depth)
                VALUES (?, ?, ?)
            ''', (row['cell_name'], row['time_series'], row['cell_depth']))
        
        # 插入时间点数据
        for i, time_point in enumerate(self.time_points):
            cursor.execute('''
                INSERT OR REPLACE INTO time_points (time_series, sequence_order)
                VALUES (?, ?)
            ''', (time_point, i))
        
        conn.commit()
        conn.close()
        
        print(f"数据库创建完成: {self.db_path}")
        
    def generate_geojson_by_time(self):
        """为每个时间点生成GeoJSON文件"""
        print("正在生成时间序列GeoJSON文件...")

        geojson_dir = self.output_dir / "geojson"
        geojson_dir.mkdir(exist_ok=True)

        # 预先转换所有几何体为GeoJSON格式，避免重复转换
        print("预处理几何数据...")
        geometry_cache = {}
        for idx, row in self.gdf.iterrows():
            cell_name = row['cell_name']
            # 直接使用__geo_interface__获取几何体，更高效
            geometry_cache[cell_name] = row.geometry.__geo_interface__

        total_time_points = len(self.time_points)
        for i, time_point in enumerate(self.time_points):
            print(f"处理时间点 {i+1}/{total_time_points}: {time_point}")

            # 获取该时间点的水深数据
            time_depths = self.depth_matrix[time_point].to_dict()

            # 创建GeoJSON特征集合
            features = []
            for idx, row in self.gdf.iterrows():
                cell_name = row['cell_name']
                depth = time_depths.get(cell_name, 0)

                feature = {
                    "type": "Feature",
                    "properties": {
                        "cell_name": cell_name,
                        "depth": depth,
                        "time": time_point
                    },
                    "geometry": geometry_cache[cell_name]
                }
                features.append(feature)

            geojson_data = {
                "type": "FeatureCollection",
                "features": features
            }

            # 保存GeoJSON文件（清理文件名）
            safe_time_point = self._sanitize_filename(time_point)
            output_file = geojson_dir / f"flood_{safe_time_point}.geojson"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(geojson_data, f, ensure_ascii=False, indent=2)

        print(f"GeoJSON文件生成完成，共 {len(self.time_points)} 个文件")
        
    def generate_combined_geojson(self):
        """生成包含所有时间点的合并GeoJSON文件"""
        print("正在生成合并的GeoJSON文件...")

        # 检查坐标系并转换
        self._check_and_convert_crs()

        features = []
        total_features = len(self.gdf)

        for idx, row in self.gdf.iterrows():
            if idx % 100 == 0:  # 每100个网格显示一次进度
                print(f"处理网格 {idx+1}/{total_features}")

            cell_name = row['cell_name']

            # 获取该网格的所有时间点水深数据
            time_depths = {}
            for time_point in self.time_points:
                depth = self.depth_matrix.loc[cell_name, time_point] if cell_name in self.depth_matrix.index else 0
                time_depths[str(time_point)] = depth

            feature = {
                "type": "Feature",
                "properties": {
                    "cell_name": cell_name,
                    "depths": time_depths,
                    "max_depth": max(time_depths.values()) if time_depths.values() else 0,
                    "min_depth": min(time_depths.values()) if time_depths.values() else 0
                },
                "geometry": row.geometry.__geo_interface__
            }
            features.append(feature)
        
        geojson_data = {
            "type": "FeatureCollection",
            "properties": {
                "time_points": self.time_points,
                "description": "5年一遇降雨内涝结果"
            },
            "features": features
        }
        
        # 保存合并的GeoJSON文件
        output_file = self.output_dir / "flood_combined.geojson"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(geojson_data, f, ensure_ascii=False, indent=2)

        # 同时保存到Web应用数据目录
        web_data_dir = self.output_dir.parent / "web" / "data"
        web_data_dir.mkdir(parents=True, exist_ok=True)
        web_output_file = web_data_dir / "flood_combined.geojson"
        with open(web_output_file, 'w', encoding='utf-8') as f:
            json.dump(geojson_data, f, ensure_ascii=False, indent=2)

        print(f"合并GeoJSON文件生成完成: {output_file}")
        print(f"Web应用数据文件生成完成: {web_output_file}")
        
    def generate_statistics(self):
        """生成统计信息"""
        print("正在生成统计信息...")
        
        stats = {
            "grid_count": len(self.gdf),
            "time_points": len(self.time_points),
            "time_range": self.time_points,
            "depth_statistics": {
                "max_depth": float(self.df['cell_depth'].max()),
                "min_depth": float(self.df['cell_depth'].min()),
                "mean_depth": float(self.df['cell_depth'].mean()),
                "std_depth": float(self.df['cell_depth'].std())
            },
            "spatial_extent": {
                "bounds": list(self.gdf.total_bounds),
                "crs": str(self.gdf.crs)
            }
        }
        
        # 保存统计信息
        stats_file = self.output_dir / "statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        # 同时保存到Web应用数据目录
        web_data_dir = self.output_dir.parent / "web" / "data"
        web_data_dir.mkdir(parents=True, exist_ok=True)
        web_stats_file = web_data_dir / "statistics.json"
        with open(web_stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        print(f"统计信息生成完成: {stats_file}")
        print(f"Web应用统计文件生成完成: {web_stats_file}")
        return stats
        
    def process_all(self, skip_time_series_geojson=False):
        """执行完整的数据处理流程"""
        print("开始数据处理流程...")

        self.load_data()
        self.process_time_series()
        self.create_database()

        if not skip_time_series_geojson:
            self.generate_geojson_by_time()
        else:
            print("跳过时间序列GeoJSON文件生成（仅生成合并文件）")

        self.generate_combined_geojson()
        stats = self.generate_statistics()
        
        print("数据处理完成！")
        print(f"处理结果:")
        print(f"- 网格数量: {stats['grid_count']}")
        print(f"- 时间点数量: {stats['time_points']}")
        print(f"- 最大水深: {stats['depth_statistics']['max_depth']:.2f}m")
        print(f"- 输出目录: {self.output_dir}")

def main():
    """主函数"""
    # 获取脚本所在目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent

    # 构建绝对路径
    shp_path = project_root / "data" / "flood_grid.shp"
    csv_path = project_root / "data" / "flood_depth.csv"
    output_dir = project_root / "database"

    print(f"脚本目录: {script_dir}")
    print(f"项目根目录: {project_root}")
    print(f"SHP文件路径: {shp_path}")
    print(f"CSV文件路径: {csv_path}")
    print(f"输出目录: {output_dir}")

    # 检查文件是否存在
    if not shp_path.exists():
        print(f"错误: SHP文件不存在: {shp_path}")
        print("请确保数据文件存在，或运行 create_sample_data.py 生成示例数据")
        return

    if not csv_path.exists():
        print(f"错误: CSV文件不存在: {csv_path}")
        print("请确保数据文件存在，或运行 create_sample_data.py 生成示例数据")
        return

    processor = FloodDataProcessor(str(shp_path), str(csv_path), str(output_dir))
    # 跳过时间序列GeoJSON生成以提高速度（Web应用主要使用合并文件）
    processor.process_all(skip_time_series_geojson=True)

if __name__ == "__main__":
    main()
