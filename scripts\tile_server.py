#!/usr/bin/env python3
"""
简单的矢量瓦片服务器
用于提供MBTiles文件的瓦片服务
"""

import sqlite3
import os
import sys
from pathlib import Path
from http.server import HTTPServer, BaseHTTPRequestHandler
import urllib.parse
import gzip
import json

class TileHandler(BaseHTTPRequestHandler):
    """瓦片请求处理器"""
    
    def __init__(self, *args, mbtiles_path=None, **kwargs):
        self.mbtiles_path = mbtiles_path
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        self._handle_request()

    def do_HEAD(self):
        """处理HEAD请求"""
        self._handle_request(head_only=True)

    def _handle_request(self, head_only=False):
        """处理请求的通用方法"""
        try:
            # 解析URL路径
            parsed_path = urllib.parse.urlparse(self.path)
            path_parts = parsed_path.path.strip('/').split('/')

            # 设置CORS头
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            
            # 处理瓦片请求 /tiles/{z}/{x}/{y}.pbf
            if len(path_parts) >= 4 and path_parts[0] == 'tiles':
                z = int(path_parts[1])
                x = int(path_parts[2])
                y_with_ext = path_parts[3]
                y = int(y_with_ext.split('.')[0])

                tile_data = self.get_tile(z, x, y)

                if tile_data:
                    self.send_response(200)
                    self.send_header('Content-Type', 'application/x-protobuf')
                    self.send_header('Content-Encoding', 'gzip')
                    self.send_header('Content-Length', str(len(tile_data)))
                    self.end_headers()
                    if not head_only:
                        self.wfile.write(tile_data)
                else:
                    self.send_response(404)
                    self.end_headers()

            # 处理元数据请求 /metadata
            elif path_parts[0] == 'metadata':
                metadata = self.get_metadata()
                if metadata:
                    response_data = json.dumps(metadata).encode()
                    self.send_response(200)
                    self.send_header('Content-Type', 'application/json')
                    self.send_header('Content-Length', str(len(response_data)))
                    self.end_headers()
                    if not head_only:
                        self.wfile.write(response_data)
                else:
                    self.send_response(404)
                    self.end_headers()

            else:
                self.send_response(404)
                self.end_headers()
                
        except Exception as e:
            print(f"请求处理错误: {e}")
            self.send_response(500)
            self.end_headers()
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def get_tile(self, z, x, y):
        """从MBTiles文件获取瓦片"""
        if not self.mbtiles_path or not os.path.exists(self.mbtiles_path):
            return None
            
        try:
            conn = sqlite3.connect(self.mbtiles_path)
            cursor = conn.cursor()
            
            # MBTiles使用TMS坐标系，需要转换Y坐标
            tms_y = (2 ** z) - 1 - y
            
            cursor.execute(
                "SELECT tile_data FROM tiles WHERE zoom_level = ? AND tile_column = ? AND tile_row = ?",
                (z, x, tms_y)
            )
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return result[0]
            else:
                return None
                
        except Exception as e:
            print(f"获取瓦片错误: {e}")
            return None
    
    def get_metadata(self):
        """获取MBTiles元数据"""
        if not self.mbtiles_path or not os.path.exists(self.mbtiles_path):
            return None
            
        try:
            conn = sqlite3.connect(self.mbtiles_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT name, value FROM metadata")
            metadata_rows = cursor.fetchall()
            
            metadata = {}
            for name, value in metadata_rows:
                metadata[name] = value
            
            conn.close()
            return metadata
            
        except Exception as e:
            print(f"获取元数据错误: {e}")
            return None
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.date_time_string()}] {format % args}")

def create_tile_handler(mbtiles_path):
    """创建瓦片处理器工厂函数"""
    def handler(*args, **kwargs):
        return TileHandler(*args, mbtiles_path=mbtiles_path, **kwargs)
    return handler

def start_tile_server(mbtiles_path, port=8080, host='localhost'):
    """启动瓦片服务器"""
    if not os.path.exists(mbtiles_path):
        print(f"错误: MBTiles文件不存在: {mbtiles_path}")
        return False
    
    handler_class = create_tile_handler(mbtiles_path)
    
    try:
        server = HTTPServer((host, port), handler_class)
        print(f"瓦片服务器启动成功!")
        print(f"服务地址: http://{host}:{port}")
        print(f"MBTiles文件: {mbtiles_path}")
        print(f"瓦片URL模板: http://{host}:{port}/tiles/{{z}}/{{x}}/{{y}}.pbf")
        print(f"元数据URL: http://{host}:{port}/metadata")
        print("按 Ctrl+C 停止服务器")
        
        server.serve_forever()
        
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"错误: 端口 {port} 已被占用")
            print(f"请尝试使用其他端口")
            return False
        else:
            print(f"启动服务器时出错: {e}")
            return False
    except KeyboardInterrupt:
        print("\n瓦片服务器已停止")
        return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='启动矢量瓦片服务器')
    parser.add_argument('mbtiles', help='MBTiles文件路径')
    parser.add_argument('--port', '-p', type=int, default=8080, 
                       help='端口号 (默认: 8080)')
    parser.add_argument('--host', type=str, default='localhost',
                       help='主机地址 (默认: localhost)')
    
    args = parser.parse_args()
    
    # 检查MBTiles文件
    mbtiles_path = Path(args.mbtiles)
    if not mbtiles_path.exists():
        # 尝试相对于脚本目录查找
        script_dir = Path(__file__).parent
        project_root = script_dir.parent
        mbtiles_path = project_root / 'web' / 'data' / 'flood_combined.mbtiles'
        
        if not mbtiles_path.exists():
            print(f"错误: 找不到MBTiles文件")
            print(f"请确保文件存在: {mbtiles_path}")
            sys.exit(1)
    
    print(f"使用MBTiles文件: {mbtiles_path}")
    
    # 启动服务器
    success = start_tile_server(str(mbtiles_path), args.port, args.host)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
