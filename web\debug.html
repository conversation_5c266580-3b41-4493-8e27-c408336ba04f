<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内涝数据调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>内涝数据调试页面</h1>
        
        <div class="section">
            <h3>数据加载状态</h3>
            <div id="load-status" class="status info">正在检查数据文件...</div>
            <button onclick="loadData()">重新加载数据</button>
            <button onclick="checkFiles()">检查文件</button>
        </div>
        
        <div class="grid">
            <div class="section">
                <h3>GeoJSON数据信息</h3>
                <div id="geojson-info">
                    <p>等待数据加载...</p>
                </div>
            </div>
            
            <div class="section">
                <h3>统计信息</h3>
                <div id="stats-info">
                    <p>等待数据加载...</p>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>时间点信息</h3>
            <div id="time-info">
                <p>等待数据加载...</p>
            </div>
        </div>
        
        <div class="section">
            <h3>示例特征数据</h3>
            <div id="sample-feature">
                <p>等待数据加载...</p>
            </div>
        </div>
        
        <div class="section">
            <h3>控制台日志</h3>
            <div id="console-log">
                <pre id="log-content"></pre>
            </div>
            <button onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <script>
        let floodData = null;
        let statsData = null;
        let logs = [];

        // 重写console.log来捕获日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog('LOG', args.join(' '));
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addLog('ERROR', args.join(' '));
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addLog('WARN', args.join(' '));
        };

        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push(`[${timestamp}] ${type}: ${message}`);
            updateLogDisplay();
        }

        function updateLogDisplay() {
            const logContent = document.getElementById('log-content');
            logContent.textContent = logs.slice(-20).join('\n'); // 只显示最近20条
        }

        function clearLog() {
            logs = [];
            updateLogDisplay();
        }

        async function checkFiles() {
            console.log('检查数据文件...');
            
            const files = [
                './data/flood_combined.geojson',
                './data/statistics.json'
            ];
            
            for (const file of files) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        console.log(`✓ ${file} - 状态: ${response.status}`);
                    } else {
                        console.error(`✗ ${file} - 状态: ${response.status} ${response.statusText}`);
                    }
                } catch (error) {
                    console.error(`✗ ${file} - 错误: ${error.message}`);
                }
            }
        }

        async function loadData() {
            console.log('开始加载数据...');
            
            try {
                // 加载GeoJSON数据
                console.log('加载GeoJSON数据...');
                const geoResponse = await fetch('./data/flood_combined.geojson');
                
                if (!geoResponse.ok) {
                    throw new Error(`GeoJSON加载失败: ${geoResponse.status} ${geoResponse.statusText}`);
                }
                
                floodData = await geoResponse.json();
                console.log('GeoJSON数据加载成功');
                
                // 加载统计数据
                console.log('加载统计数据...');
                const statsResponse = await fetch('./data/statistics.json');
                
                if (statsResponse.ok) {
                    statsData = await statsResponse.json();
                    console.log('统计数据加载成功');
                } else {
                    console.warn('统计数据加载失败，但继续处理');
                }
                
                updateDisplay();
                
                document.getElementById('load-status').className = 'status success';
                document.getElementById('load-status').textContent = '数据加载成功！';
                
            } catch (error) {
                console.error('数据加载失败:', error);
                document.getElementById('load-status').className = 'status error';
                document.getElementById('load-status').textContent = `数据加载失败: ${error.message}`;
            }
        }

        function updateDisplay() {
            // 更新GeoJSON信息
            if (floodData) {
                const geoInfo = document.getElementById('geojson-info');
                geoInfo.innerHTML = `
                    <p><strong>类型:</strong> ${floodData.type}</p>
                    <p><strong>特征数量:</strong> ${floodData.features?.length || 0}</p>
                    <p><strong>属性:</strong> ${JSON.stringify(floodData.properties || {}, null, 2)}</p>
                `;
                
                // 更新时间点信息
                const timeInfo = document.getElementById('time-info');
                const timePoints = floodData.properties?.time_points || [];
                if (timePoints.length === 0 && floodData.features.length > 0) {
                    const sampleFeature = floodData.features[0];
                    if (sampleFeature.properties.depths) {
                        const extractedTimes = Object.keys(sampleFeature.properties.depths).sort();
                        timeInfo.innerHTML = `
                            <p><strong>时间点数量:</strong> ${extractedTimes.length}</p>
                            <p><strong>时间范围:</strong></p>
                            <pre>${extractedTimes.slice(0, 10).join('\n')}${extractedTimes.length > 10 ? '\n...' : ''}</pre>
                        `;
                    }
                } else {
                    timeInfo.innerHTML = `
                        <p><strong>时间点数量:</strong> ${timePoints.length}</p>
                        <p><strong>时间范围:</strong></p>
                        <pre>${timePoints.slice(0, 10).join('\n')}${timePoints.length > 10 ? '\n...' : ''}</pre>
                    `;
                }
                
                // 更新示例特征
                const sampleFeature = document.getElementById('sample-feature');
                if (floodData.features.length > 0) {
                    const feature = floodData.features[0];
                    sampleFeature.innerHTML = `
                        <p><strong>第一个特征:</strong></p>
                        <pre>${JSON.stringify(feature, null, 2).substring(0, 1000)}...</pre>
                    `;
                }
            }
            
            // 更新统计信息
            if (statsData) {
                const statsInfo = document.getElementById('stats-info');
                statsInfo.innerHTML = `
                    <pre>${JSON.stringify(statsData, null, 2)}</pre>
                `;
            }
        }

        // 页面加载时自动检查和加载数据
        window.addEventListener('load', () => {
            console.log('调试页面加载完成');
            checkFiles();
            loadData();
        });
    </script>
</body>
</html>
