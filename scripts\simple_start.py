#!/usr/bin/env python3
"""
简化的启动脚本
"""

import subprocess
import sys
import time
import os
from pathlib import Path

def start_web_server():
    """启动Web服务器"""
    script_dir = Path(__file__).parent
    web_dir = script_dir.parent / 'web'
    
    print(f"启动Web服务器...")
    print(f"目录: {web_dir}")
    print(f"地址: http://localhost:8001")
    
    os.chdir(web_dir)
    
    try:
        # 使用Python内置的HTTP服务器
        subprocess.run([sys.executable, '-m', 'http.server', '8001'])
    except KeyboardInterrupt:
        print("\nWeb服务器已停止")

def main():
    """主函数"""
    print("启动Web服务器")
    print("=" * 30)
    print("Web应用将在 http://localhost:8001 运行")
    print("按 Ctrl+C 停止服务器")
    print("=" * 30)
    
    start_web_server()

if __name__ == "__main__":
    main()
