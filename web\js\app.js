/**
 * 内涝积水动态演示应用主类
 */
class FloodVisualizationApp {
    constructor() {
        // 配置信息
        this.config = {
            mapboxToken: 'pk.eyJ1IjoicmlkZGxlOTExIiwiYSI6ImNqNXVxZGp3aTFyMmIycW4yaHVuenhkY3IifQ.Or4MfHvoQOSmUnB57FCfHQ', // 需要替换为实际的Mapbox token
            defaultCenter: [116.4074, 39.9042], // 北京坐标，需要根据实际数据调整
            defaultZoom: 10,
            dataPath: './data/', // 数据文件路径
            useLocalTiles: true // 是否使用本地瓦片
        };
        
        this.map = null;
        this.timeController = null;
        this.floodData = null;
        this.currentTimeData = null;
        this.statistics = null;
        this.dataBounds = null;
        
        this.init();
    }
    
    /**
     * 初始化应用
     */
    async init() {
        try {
            // 显示加载指示器
            this.showLoading(true);
            
            // 初始化时间控制器
            this.timeController = new TimeController();
            this.timeController.onTimeChange((time, index) => {
                this.updateVisualization(time, index);
            });
            this.timeController.onPlayStart(() => {
                this.onAnimationStart();
            });
            
            // 初始化地图
            await this.initMap();
            
            // 加载数据
            await this.loadData();
            
            // 初始化控件
            this.initControls();
            
            // 隐藏加载指示器
            this.showLoading(false);
            
            console.log('应用初始化完成');
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showError('应用初始化失败: ' + error.message);
        }
    }
    
    /**
     * 初始化Mapbox地图
     */
    async initMap() {
        // 检查是否有Mapbox token       
        mapboxgl.accessToken = this.config.mapboxToken;
        
        this.map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/light-v11',
            center: this.dataBounds ? this.dataBounds.center : this.config.defaultCenter,
            zoom: this.dataBounds ? 12 : this.config.defaultZoom,
            antialias: true
        });
        
        // 地图加载完成后的处理
        this.map.on('load', () => {
            console.log('Mapbox地图加载完成');
            this.onMapLoaded();

            // 如果数据已经加载，立即显示
            if (this.floodData && this.timeController.getCurrentTime()) {
                console.log('地图加载完成，开始显示洪水数据');
                this.updateMapLayers();
            }
        });
        
        // 添加导航控件
        this.map.addControl(new mapboxgl.NavigationControl());
        
        // 添加比例尺
        this.map.addControl(new mapboxgl.ScaleControl());
    }
    
    /**
     * 初始化开源地图（当没有Mapbox token时）
     */
    initOpenSourceMap() {
        // 使用简单的SVG可视化作为替代
        console.log('使用简化地图方案');

        // 创建简单的地图容器
        const mapContainer = document.getElementById('map');
        mapContainer.innerHTML = `
            <div id="simple-map" style="
                width: 100%;
                height: 100%;
                background: #f0f8ff;
                position: relative;
                overflow: hidden;
            ">
                <div style="
                    position: absolute;
                    top: 10px;
                    left: 10px;
                    background: rgba(255,255,255,0.9);
                    padding: 10px;
                    border-radius: 5px;
                    font-size: 14px;
                    color: #666;
                ">
                    <h3>内涝积水可视化</h3>
                    <p>简化显示模式</p>
                    <p>配置Mapbox Token可获得完整地图</p>
                </div>
                <svg id="flood-svg" width="100%" height="100%">
                </svg>
            </div>
        `;

        // 模拟地图加载完成
        setTimeout(() => {
            this.onMapLoaded();

            // 如果数据已经加载，立即显示
            if (this.floodData && this.timeController.getCurrentTime()) {
                console.log('简化地图准备完成，开始显示洪水数据');
                this.updateSimpleVisualization();
            }
        }, 100);
    }
    
    /**
     * 地图加载完成后的处理
     */
    onMapLoaded() {
        console.log('地图准备就绪');

        // 如果数据已加载，调整地图视图到数据范围
        if (this.dataBounds) {
            this.fitMapToData();
        }
    }

    /**
     * 计算数据的地理边界
     */
    calculateDataBounds() {
        if (!this.floodData || !this.floodData.features || this.floodData.features.length === 0) {
            console.warn('无法计算数据边界：没有特征数据');
            return;
        }

        let minLng = Infinity, minLat = Infinity;
        let maxLng = -Infinity, maxLat = -Infinity;

        this.floodData.features.forEach(feature => {
            if (feature.geometry && feature.geometry.type === 'Polygon') {
                const coordinates = feature.geometry.coordinates[0]; // 外环坐标
                coordinates.forEach(coord => {
                    const [lng, lat] = coord;
                    minLng = Math.min(minLng, lng);
                    maxLng = Math.max(maxLng, lng);
                    minLat = Math.min(minLat, lat);
                    maxLat = Math.max(maxLat, lat);
                });
            }
        });

        if (minLng !== Infinity) {
            this.dataBounds = {
                southwest: [minLng, minLat],
                northeast: [maxLng, maxLat],
                center: [(minLng + maxLng) / 2, (minLat + maxLat) / 2]
            };

            console.log('数据边界计算完成:', this.dataBounds);
        } else {
            console.warn('无法计算有效的数据边界');
        }
    }

    /**
     * 调整地图视图到数据范围
     */
    fitMapToData() {
        if (!this.dataBounds) {
            console.warn('无法调整地图视图：没有数据边界信息');
            return;
        }

        // 显示视图调整提示
        this.showViewAdjustmentNotification();

        if (this.map && this.map.fitBounds) {
            // Mapbox地图
            try {
                console.log('调整Mapbox地图视图到数据范围');
                this.map.fitBounds([
                    this.dataBounds.southwest,
                    this.dataBounds.northeast
                ], {
                    padding: 50,
                    maxZoom: 15,
                    duration: 2000 // 2秒动画
                });
            } catch (error) {
                console.error('调整Mapbox地图视图失败:', error);
                // 备用方案：直接设置中心点和缩放级别
                this.map.setCenter(this.dataBounds.center);
                this.map.setZoom(12);
            }
        } else {
            // 简化地图或其他情况
            console.log('调整简化地图视图到数据中心');
            this.config.defaultCenter = this.dataBounds.center;
            this.config.defaultZoom = 12;
        }
    }

    /**
     * 显示视图调整通知
     */
    showViewAdjustmentNotification() {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(33, 150, 243, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 10000;
            transition: opacity 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;
        notification.textContent = '🎯 正在调整视图到数据范围...';

        document.body.appendChild(notification);

        // 2.5秒后自动移除通知
        setTimeout(() => {
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 2500);
    }
    
    /**
     * 加载数据
     */
    async loadData() {
        try {
            console.log('开始加载数据文件...');
            console.log('数据路径:', this.config.dataPath + 'flood_combined.geojson');

            // 尝试加载合并的GeoJSON数据
            const response = await fetch(this.config.dataPath + 'flood_combined.geojson');

            console.log('响应状态:', response.status, response.statusText);

            if (!response.ok) {
                throw new Error(`无法加载数据文件: ${response.status} ${response.statusText}`);
            }

            this.floodData = await response.json();

            console.log('GeoJSON数据加载成功:', {
                type: this.floodData.type,
                features: this.floodData.features?.length || 0,
                properties: this.floodData.properties
            });

            // 提取时间点
            const timePoints = this.floodData.properties?.time_points || [];
            if (timePoints.length === 0) {
                // 如果没有预定义时间点，从数据中提取
                const sampleFeature = this.floodData.features[0];
                if (sampleFeature && sampleFeature.properties.depths) {
                    const extractedTimePoints = Object.keys(sampleFeature.properties.depths).sort();
                    timePoints.push(...extractedTimePoints);
                    console.log('从特征数据中提取时间点:', extractedTimePoints);
                }
            }

            console.log('时间点:', timePoints);

            // 设置时间控制器
            this.timeController.setTimePoints(timePoints);

            // 计算数据边界并调整地图视图
            this.calculateDataBounds();
            this.fitMapToData();

            // 加载统计信息
            await this.loadStatistics();

            console.log('数据加载完成:', {
                features: this.floodData.features.length,
                timePoints: timePoints.length,
                bounds: this.dataBounds
            });

            // 检查第一个特征的数据结构
            if (this.floodData.features.length > 0) {
                console.log('第一个特征示例:', this.floodData.features[0]);

                // 数据加载完成后，如果地图已准备好，立即调整视图
                setTimeout(() => {
                    if (this.map && (this.map.loaded() || this.map.loaded === undefined)) {
                        console.log('数据加载完成，自动调整地图视图');
                        this.fitMapToData();

                        // 显示第一个时间点的数据
                        if (this.timeController.getCurrentTime()) {
                            this.updateMapLayers();
                        }
                    }
                }, 500); // 延迟500ms确保地图完全加载
            }

        } catch (error) {
            console.error('数据加载失败:', error);
            // 加载示例数据
            this.loadSampleData();
        }
    }
    
    /**
     * 加载统计信息
     */
    async loadStatistics() {
        try {
            const response = await fetch(this.config.dataPath + 'statistics.json');
            if (response.ok) {
                this.statistics = await response.json();
            }
        } catch (error) {
            console.warn('统计信息加载失败:', error);
        }
    }
    
    /**
     * 加载示例数据（当真实数据不可用时）
     */
    loadSampleData() {
        console.log('加载示例数据');
        
        // 创建示例时间点
        const timePoints = ['0h', '1h', '2h', '3h', '4h', '5h', '6h'];
        this.timeController.setTimePoints(timePoints);
        
        // 创建示例数据
        this.floodData = {
            type: 'FeatureCollection',
            properties: {
                time_points: timePoints,
                description: '示例内涝数据'
            },
            features: this.generateSampleFeatures(timePoints)
        };
        
        console.log('示例数据生成完成');
    }
    
    /**
     * 生成示例特征数据
     */
    generateSampleFeatures(timePoints) {
        const features = [];
        const centerLon = this.config.defaultCenter[0];
        const centerLat = this.config.defaultCenter[1];
        
        // 生成网格
        for (let i = 0; i < 100; i++) {
            const offsetLon = (Math.random() - 0.5) * 0.1;
            const offsetLat = (Math.random() - 0.5) * 0.1;
            
            const depths = {};
            timePoints.forEach((time, index) => {
                // 模拟水深变化
                const baseDepth = Math.random() * 2;
                const timeMultiplier = Math.sin((index / timePoints.length) * Math.PI);
                depths[time] = Math.max(0, baseDepth * timeMultiplier);
            });
            
            features.push({
                type: 'Feature',
                properties: {
                    cell_name: `cell_${i}`,
                    depths: depths,
                    max_depth: Math.max(...Object.values(depths)),
                    min_depth: Math.min(...Object.values(depths))
                },
                geometry: {
                    type: 'Polygon',
                    coordinates: [[
                        [centerLon + offsetLon, centerLat + offsetLat],
                        [centerLon + offsetLon + 0.01, centerLat + offsetLat],
                        [centerLon + offsetLon + 0.01, centerLat + offsetLat + 0.01],
                        [centerLon + offsetLon, centerLat + offsetLat + 0.01],
                        [centerLon + offsetLon, centerLat + offsetLat]
                    ]]
                }
            });
        }
        
        return features;
    }
    
    /**
     * 初始化控件
     */
    initControls() {
        // 透明度控制
        const opacitySlider = document.getElementById('opacity-slider');
        const opacityDisplay = document.getElementById('opacity-display');
        
        opacitySlider.addEventListener('input', (e) => {
            const opacity = parseFloat(e.target.value);
            opacityDisplay.textContent = Math.round(opacity * 100) + '%';
            this.updateLayerOpacity(opacity);
        });
        
        // 网格边界显示控制
        const showGridCheckbox = document.getElementById('show-grid');
        showGridCheckbox.addEventListener('change', (e) => {
            this.toggleGridBorders(e.target.checked);
        });
        
        // 标签显示控制
        const showLabelsCheckbox = document.getElementById('show-labels');
        showLabelsCheckbox.addEventListener('change', (e) => {
            this.toggleLabels(e.target.checked);
        });
    }
    
    /**
     * 更新可视化
     */
    updateVisualization(time, index) {
        if (!this.floodData) {
            console.warn('更新可视化失败: 没有洪水数据');
            return;
        }

        console.log('更新可视化:', time, index);
        console.log('当前洪水数据特征数量:', this.floodData.features.length);

        // 更新当前时间的数据
        this.updateCurrentTimeData(time);

        console.log('当前时间数据更新完成，统计信息:', this.currentStats);

        // 更新统计信息显示
        this.updateStatisticsDisplay();

        // 如果有地图，更新地图图层
        if (this.map && this.map.loaded()) {
            this.updateMapLayers();
        } else {
            console.log('地图未加载或不可用，尝试简化可视化');
            this.updateSimpleVisualization();
        }
    }
    
    /**
     * 更新当前时间的数据
     */
    updateCurrentTimeData(time) {
        if (!this.floodData || !time) return;

        // 优化：在一次遍历中计算所有统计信息
        let maxDepth = 0;
        let totalDepth = 0;
        let floodedCount = 0;
        const features = this.floodData.features;

        this.currentTimeData = [];

        for (let i = 0; i < features.length; i++) {
            const feature = features[i];
            const depth = feature.properties.depths?.[time] || 0;

            // 更新统计信息
            if (depth > maxDepth) {
                maxDepth = depth;
            }
            totalDepth += depth;
            if (depth > 0.1) {
                floodedCount++;
            }

            // 只存储必要的信息
            this.currentTimeData.push({
                properties: {
                    cell_name: feature.properties.cell_name,
                    current_depth: depth
                }
            });
        }

        // 存储统计信息
        this.currentStats = {
            maxDepth: maxDepth,
            avgDepth: features.length > 0 ? totalDepth / features.length : 0,
            floodedCount: floodedCount
        };
    }
    
    /**
     * 更新统计信息显示
     */
    updateStatisticsDisplay() {
        if (!this.currentStats) return;

        // 直接使用预计算的统计信息
        document.getElementById('max-depth').textContent = this.currentStats.maxDepth.toFixed(2) + 'm';
        document.getElementById('avg-depth').textContent = this.currentStats.avgDepth.toFixed(2) + 'm';
        document.getElementById('flooded-area').textContent = this.currentStats.floodedCount + ' 网格';
    }
    
    /**
     * 更新地图图层
     */
    updateMapLayers() {
        console.log('更新地图图层');

        if (!this.map || !this.currentTimeData) {
            console.log('地图或当前时间数据不可用');
            return;
        }

        try {
            // 如果是Mapbox地图，添加数据源和图层
            if (this.map.addSource && !this.map.getSource('flood-data')) {
                console.log('添加洪水数据源到地图');

                // 创建当前时间的GeoJSON数据
                const currentGeoJSON = {
                    type: 'FeatureCollection',
                    features: this.floodData.features.map(feature => {
                        const currentTime = this.timeController.getCurrentTime();
                        const depth = feature.properties.depths?.[currentTime] || 0;

                        return {
                            type: 'Feature',
                            properties: {
                                ...feature.properties,
                                current_depth: depth
                            },
                            geometry: feature.geometry
                        };
                    })
                };

                // 添加数据源
                this.map.addSource('flood-data', {
                    type: 'geojson',
                    data: currentGeoJSON
                });

                // 添加填充图层
                this.map.addLayer({
                    id: 'flood-fill',
                    type: 'fill',
                    source: 'flood-data',
                    paint: {
                        'fill-color': [
                            'interpolate',
                            ['linear'],
                            ['get', 'current_depth'],
                            0, 'rgba(0, 0, 255, 0)',
                            0.1, 'rgba(173, 216, 230, 0.6)',
                            0.5, 'rgba(0, 191, 255, 0.7)',
                            1.0, 'rgba(30, 144, 255, 0.8)',
                            2.0, 'rgba(0, 0, 255, 0.9)'
                        ],
                        'fill-opacity': 0.8
                    }
                });

                // 添加边界图层
                this.map.addLayer({
                    id: 'flood-outline',
                    type: 'line',
                    source: 'flood-data',
                    paint: {
                        'line-color': '#000',
                        'line-width': 0.5,
                        'line-opacity': 0.3
                    }
                });

                console.log('洪水图层添加完成');
            } else if (this.map.getSource('flood-data')) {
                // 更新现有数据源
                const currentTime = this.timeController.getCurrentTime();
                const currentGeoJSON = {
                    type: 'FeatureCollection',
                    features: this.floodData.features.map(feature => {
                        const depth = feature.properties.depths?.[currentTime] || 0;

                        return {
                            type: 'Feature',
                            properties: {
                                ...feature.properties,
                                current_depth: depth
                            },
                            geometry: feature.geometry
                        };
                    })
                };

                this.map.getSource('flood-data').setData(currentGeoJSON);
                console.log('洪水数据已更新，当前时间:', currentTime);
            }
        } catch (error) {
            console.error('更新地图图层时出错:', error);
        }
    }
    
    /**
     * 更新图层透明度
     */
    updateLayerOpacity(opacity) {
        console.log('更新透明度:', opacity);
    }
    
    /**
     * 切换网格边界显示
     */
    toggleGridBorders(show) {
        console.log('切换网格边界:', show);
    }
    
    /**
     * 切换标签显示
     */
    toggleLabels(show) {
        console.log('切换标签显示:', show);
    }
    
    /**
     * 显示/隐藏加载指示器
     */
    showLoading(show) {
        const loading = document.getElementById('loading');
        loading.style.display = show ? 'flex' : 'none';
    }
    
    /**
     * 动画开始时的处理
     */
    onAnimationStart() {
        console.log('动画开始，自动调整视图到数据范围');

        // 调整地图视图到数据范围
        this.fitMapToData();

        // 如果是第一次播放，确保显示数据
        if (this.floodData && this.timeController.getCurrentTime()) {
            if (this.map && this.map.loaded()) {
                this.updateMapLayers();
            } else {
                this.updateSimpleVisualization();
            }
        }
    }

    /**
     * 简化可视化（当没有地图时）
     */
    updateSimpleVisualization() {
        const svg = document.getElementById('flood-svg');
        if (!svg || !this.floodData || !this.currentStats) {
            console.log('简化可视化条件不满足');
            return;
        }

        console.log('更新简化可视化');

        // 清除现有内容
        svg.innerHTML = '';

        // 创建简单的网格可视化
        const currentTime = this.timeController.getCurrentTime();
        const features = this.floodData.features;
        const svgRect = svg.getBoundingClientRect();

        // 如果有地理边界信息，使用地理布局；否则使用网格布局
        let useGeographicLayout = false;

        if (this.dataBounds) {
            useGeographicLayout = true;
            const boundsWidth = this.dataBounds.northeast[0] - this.dataBounds.southwest[0];
            const boundsHeight = this.dataBounds.northeast[1] - this.dataBounds.southwest[1];

            // 计算缩放比例
            const scaleX = (svgRect.width - 80) / boundsWidth;
            const scaleY = (svgRect.height - 80) / boundsHeight;
            const scale = Math.min(scaleX, scaleY);

            console.log('使用地理布局，缩放比例:', scale);
        }

        // 计算网格布局（备用方案）
        const cols = Math.ceil(Math.sqrt(features.length));
        const rows = Math.ceil(features.length / cols);
        const cellWidth = (svgRect.width - 40) / cols;
        const cellHeight = (svgRect.height - 40) / rows;

        // 创建颜色映射函数
        const getColor = (depth) => {
            if (depth <= 0) return '#f0f8ff';
            if (depth <= 0.1) return '#add8e6';
            if (depth <= 0.5) return '#00bfff';
            if (depth <= 1.0) return '#1e90ff';
            if (depth <= 2.0) return '#0000ff';
            return '#000080';
        };

        // 绘制网格
        features.forEach((feature, index) => {
            const depth = feature.properties.depths?.[currentTime] || 0;
            let x, y, width, height;

            if (useGeographicLayout && feature.geometry && feature.geometry.type === 'Polygon') {
                // 使用地理坐标布局
                const coords = feature.geometry.coordinates[0];
                let minLng = Infinity, minLat = Infinity, maxLng = -Infinity, maxLat = -Infinity;

                coords.forEach(coord => {
                    const [lng, lat] = coord;
                    minLng = Math.min(minLng, lng);
                    maxLng = Math.max(maxLng, lng);
                    minLat = Math.min(minLat, lat);
                    maxLat = Math.max(maxLat, lat);
                });

                // 转换地理坐标到屏幕坐标
                const boundsWidth = this.dataBounds.northeast[0] - this.dataBounds.southwest[0];
                const boundsHeight = this.dataBounds.northeast[1] - this.dataBounds.southwest[1];
                const scaleX = (svgRect.width - 80) / boundsWidth;
                const scaleY = (svgRect.height - 80) / boundsHeight;
                const scale = Math.min(scaleX, scaleY);

                x = 40 + (minLng - this.dataBounds.southwest[0]) * scale;
                y = 40 + (this.dataBounds.northeast[1] - maxLat) * scale; // Y轴翻转
                width = (maxLng - minLng) * scale;
                height = (maxLat - minLat) * scale;

                // 确保最小尺寸
                width = Math.max(width, 2);
                height = Math.max(height, 2);
            } else {
                // 使用网格布局
                const row = Math.floor(index / cols);
                const col = index % cols;

                x = 20 + col * cellWidth;
                y = 20 + row * cellHeight;
                width = cellWidth - 1;
                height = cellHeight - 1;
            }

            // 创建矩形
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', x);
            rect.setAttribute('y', y);
            rect.setAttribute('width', width);
            rect.setAttribute('height', height);
            rect.setAttribute('fill', getColor(depth));
            rect.setAttribute('stroke', '#333');
            rect.setAttribute('stroke-width', '0.5');

            // 添加标题（鼠标悬停显示）
            const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
            title.textContent = `${feature.properties.cell_name}: ${depth.toFixed(3)}m`;
            rect.appendChild(title);

            svg.appendChild(rect);
        });

        // 添加图例
        const legendY = svgRect.height - 60;
        const legendSteps = [0, 0.1, 0.5, 1.0, 2.0];
        const legendWidth = 200;
        const stepWidth = legendWidth / legendSteps.length;

        legendSteps.forEach((depth, index) => {
            const x = 20 + index * stepWidth;

            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', x);
            rect.setAttribute('y', legendY);
            rect.setAttribute('width', stepWidth);
            rect.setAttribute('height', 20);
            rect.setAttribute('fill', getColor(depth));
            rect.setAttribute('stroke', '#333');
            rect.setAttribute('stroke-width', '1');
            svg.appendChild(rect);

            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', x + stepWidth / 2);
            text.setAttribute('y', legendY + 35);
            text.setAttribute('text-anchor', 'middle');
            text.setAttribute('font-size', '12');
            text.setAttribute('fill', '#333');
            text.textContent = depth + 'm';
            svg.appendChild(text);
        });

        console.log('简化可视化更新完成');
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        this.showLoading(false);
        const errorDiv = document.getElementById('error-message');
        errorDiv.querySelector('p').textContent = message;
        errorDiv.style.display = 'block';
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.floodApp = new FloodVisualizationApp();
});
