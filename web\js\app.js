/**
 * 内涝积水动态演示应用主类
 */
class FloodVisualizationApp {
    constructor() {
        // 配置信息
        this.config = {
            mapboxToken: 'pk.eyJ1IjoicmlkZGxlOTExIiwiYSI6ImNqNXVxZGp3aTFyMmIycW4yaHVuenhkY3IifQ.Or4MfHvoQOSmUnB57FCfHQ', // 需要替换为实际的Mapbox token
            defaultCenter: [116.4074, 39.9042], // 北京坐标，需要根据实际数据调整
            defaultZoom: 10,
            dataPath: './data/', // 数据文件路径
            useLocalTiles: true // 是否使用本地瓦片
        };
        
        this.map = null;
        this.timeController = null;
        this.floodData = null;
        this.currentTimeData = null;
        this.statistics = null;
        
        this.init();
    }
    
    /**
     * 初始化应用
     */
    async init() {
        try {
            // 显示加载指示器
            this.showLoading(true);
            
            // 初始化时间控制器
            this.timeController = new TimeController();
            this.timeController.onTimeChange((time, index) => {
                this.updateVisualization(time, index);
            });
            
            // 初始化地图
            await this.initMap();
            
            // 加载数据
            await this.loadData();
            
            // 初始化控件
            this.initControls();
            
            // 隐藏加载指示器
            this.showLoading(false);
            
            console.log('应用初始化完成');
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.showError('应用初始化失败: ' + error.message);
        }
    }
    
    /**
     * 初始化Mapbox地图
     */
    async initMap() {
        // 检查是否有Mapbox token       
        mapboxgl.accessToken = this.config.mapboxToken;
        
        this.map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/light-v11',
            center: this.config.defaultCenter,
            zoom: this.config.defaultZoom,
            antialias: true
        });
        
        // 地图加载完成后的处理
        this.map.on('load', () => {
            console.log('地图加载完成');
            this.onMapLoaded();
        });
        
        // 添加导航控件
        this.map.addControl(new mapboxgl.NavigationControl());
        
        // 添加比例尺
        this.map.addControl(new mapboxgl.ScaleControl());
    }
    
    /**
     * 初始化开源地图（当没有Mapbox token时）
     */
    initOpenSourceMap() {
        // 使用MapLibre GL JS作为替代
        console.log('使用开源地图方案');
        
        // 创建简单的地图容器
        const mapContainer = document.getElementById('map');
        mapContainer.innerHTML = `
            <div style="
                width: 100%; 
                height: 100%; 
                background: #f0f8ff;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                color: #666;
            ">
                <h2>地图视图</h2>
                <p>请设置Mapbox Access Token以显示完整地图</p>
                <p>或者集成本地瓦片服务</p>
            </div>
        `;
        
        // 模拟地图加载完成
        setTimeout(() => {
            this.onMapLoaded();
        }, 100);
    }
    
    /**
     * 地图加载完成后的处理
     */
    onMapLoaded() {
        console.log('地图准备就绪');
        // 这里可以添加地图相关的初始化代码
    }
    
    /**
     * 加载数据
     */
    async loadData() {
        try {
            // 尝试加载合并的GeoJSON数据
            const response = await fetch(this.config.dataPath + 'flood_combined.geojson');
            
            if (!response.ok) {
                throw new Error('无法加载数据文件');
            }
            
            this.floodData = await response.json();
            
            // 提取时间点
            const timePoints = this.floodData.properties?.time_points || [];
            if (timePoints.length === 0) {
                // 如果没有预定义时间点，从数据中提取
                const sampleFeature = this.floodData.features[0];
                if (sampleFeature && sampleFeature.properties.depths) {
                    timePoints = Object.keys(sampleFeature.properties.depths).sort();
                }
            }
            
            // 设置时间控制器
            this.timeController.setTimePoints(timePoints);
            
            // 加载统计信息
            await this.loadStatistics();
            
            console.log('数据加载完成:', {
                features: this.floodData.features.length,
                timePoints: timePoints.length
            });
            
        } catch (error) {
            console.error('数据加载失败:', error);
            // 加载示例数据
            this.loadSampleData();
        }
    }
    
    /**
     * 加载统计信息
     */
    async loadStatistics() {
        try {
            const response = await fetch(this.config.dataPath + 'statistics.json');
            if (response.ok) {
                this.statistics = await response.json();
            }
        } catch (error) {
            console.warn('统计信息加载失败:', error);
        }
    }
    
    /**
     * 加载示例数据（当真实数据不可用时）
     */
    loadSampleData() {
        console.log('加载示例数据');
        
        // 创建示例时间点
        const timePoints = ['0h', '1h', '2h', '3h', '4h', '5h', '6h'];
        this.timeController.setTimePoints(timePoints);
        
        // 创建示例数据
        this.floodData = {
            type: 'FeatureCollection',
            properties: {
                time_points: timePoints,
                description: '示例内涝数据'
            },
            features: this.generateSampleFeatures(timePoints)
        };
        
        console.log('示例数据生成完成');
    }
    
    /**
     * 生成示例特征数据
     */
    generateSampleFeatures(timePoints) {
        const features = [];
        const centerLon = this.config.defaultCenter[0];
        const centerLat = this.config.defaultCenter[1];
        
        // 生成网格
        for (let i = 0; i < 100; i++) {
            const offsetLon = (Math.random() - 0.5) * 0.1;
            const offsetLat = (Math.random() - 0.5) * 0.1;
            
            const depths = {};
            timePoints.forEach((time, index) => {
                // 模拟水深变化
                const baseDepth = Math.random() * 2;
                const timeMultiplier = Math.sin((index / timePoints.length) * Math.PI);
                depths[time] = Math.max(0, baseDepth * timeMultiplier);
            });
            
            features.push({
                type: 'Feature',
                properties: {
                    cell_name: `cell_${i}`,
                    depths: depths,
                    max_depth: Math.max(...Object.values(depths)),
                    min_depth: Math.min(...Object.values(depths))
                },
                geometry: {
                    type: 'Polygon',
                    coordinates: [[
                        [centerLon + offsetLon, centerLat + offsetLat],
                        [centerLon + offsetLon + 0.01, centerLat + offsetLat],
                        [centerLon + offsetLon + 0.01, centerLat + offsetLat + 0.01],
                        [centerLon + offsetLon, centerLat + offsetLat + 0.01],
                        [centerLon + offsetLon, centerLat + offsetLat]
                    ]]
                }
            });
        }
        
        return features;
    }
    
    /**
     * 初始化控件
     */
    initControls() {
        // 透明度控制
        const opacitySlider = document.getElementById('opacity-slider');
        const opacityDisplay = document.getElementById('opacity-display');
        
        opacitySlider.addEventListener('input', (e) => {
            const opacity = parseFloat(e.target.value);
            opacityDisplay.textContent = Math.round(opacity * 100) + '%';
            this.updateLayerOpacity(opacity);
        });
        
        // 网格边界显示控制
        const showGridCheckbox = document.getElementById('show-grid');
        showGridCheckbox.addEventListener('change', (e) => {
            this.toggleGridBorders(e.target.checked);
        });
        
        // 标签显示控制
        const showLabelsCheckbox = document.getElementById('show-labels');
        showLabelsCheckbox.addEventListener('change', (e) => {
            this.toggleLabels(e.target.checked);
        });
    }
    
    /**
     * 更新可视化
     */
    updateVisualization(time, index) {
        if (!this.floodData) return;
        
        console.log('更新可视化:', time, index);
        
        // 更新当前时间的数据
        this.updateCurrentTimeData(time);
        
        // 更新统计信息显示
        this.updateStatisticsDisplay();
        
        // 如果有地图，更新地图图层
        if (this.map && this.map.loaded()) {
            this.updateMapLayers();
        }
    }
    
    /**
     * 更新当前时间的数据
     */
    updateCurrentTimeData(time) {
        if (!this.floodData || !time) return;

        // 优化：在一次遍历中计算所有统计信息
        let maxDepth = 0;
        let totalDepth = 0;
        let floodedCount = 0;
        const features = this.floodData.features;

        this.currentTimeData = [];

        for (let i = 0; i < features.length; i++) {
            const feature = features[i];
            const depth = feature.properties.depths?.[time] || 0;

            // 更新统计信息
            if (depth > maxDepth) {
                maxDepth = depth;
            }
            totalDepth += depth;
            if (depth > 0.1) {
                floodedCount++;
            }

            // 只存储必要的信息
            this.currentTimeData.push({
                properties: {
                    cell_name: feature.properties.cell_name,
                    current_depth: depth
                }
            });
        }

        // 存储统计信息
        this.currentStats = {
            maxDepth: maxDepth,
            avgDepth: features.length > 0 ? totalDepth / features.length : 0,
            floodedCount: floodedCount
        };
    }
    
    /**
     * 更新统计信息显示
     */
    updateStatisticsDisplay() {
        if (!this.currentStats) return;

        // 直接使用预计算的统计信息
        document.getElementById('max-depth').textContent = this.currentStats.maxDepth.toFixed(2) + 'm';
        document.getElementById('avg-depth').textContent = this.currentStats.avgDepth.toFixed(2) + 'm';
        document.getElementById('flooded-area').textContent = this.currentStats.floodedCount + ' 网格';
    }
    
    /**
     * 更新地图图层
     */
    updateMapLayers() {
        // 这里实现地图图层更新逻辑
        console.log('更新地图图层');
    }
    
    /**
     * 更新图层透明度
     */
    updateLayerOpacity(opacity) {
        console.log('更新透明度:', opacity);
    }
    
    /**
     * 切换网格边界显示
     */
    toggleGridBorders(show) {
        console.log('切换网格边界:', show);
    }
    
    /**
     * 切换标签显示
     */
    toggleLabels(show) {
        console.log('切换标签显示:', show);
    }
    
    /**
     * 显示/隐藏加载指示器
     */
    showLoading(show) {
        const loading = document.getElementById('loading');
        loading.style.display = show ? 'flex' : 'none';
    }
    
    /**
     * 显示错误信息
     */
    showError(message) {
        this.showLoading(false);
        const errorDiv = document.getElementById('error-message');
        errorDiv.querySelector('p').textContent = message;
        errorDiv.style.display = 'block';
    }
}

// 应用启动
document.addEventListener('DOMContentLoaded', () => {
    window.floodApp = new FloodVisualizationApp();
});
