#!/usr/bin/env python3
"""
检查MBTiles文件结构
"""

import sqlite3
import sys
from pathlib import Path

def check_mbtiles(mbtiles_path):
    """检查MBTiles文件"""
    print(f"检查MBTiles文件: {mbtiles_path}")
    
    if not Path(mbtiles_path).exists():
        print("❌ 文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(mbtiles_path)
        cursor = conn.cursor()
        
        # 检查表结构
        print("\n📋 数据库表:")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"  - {table[0]}")
        
        # 检查元数据
        print("\n📊 元数据:")
        try:
            cursor.execute("SELECT name, value FROM metadata")
            metadata = cursor.fetchall()
            for name, value in metadata:
                print(f"  {name}: {value}")
        except sqlite3.OperationalError:
            print("  ❌ 没有metadata表")
        
        # 检查瓦片
        print("\n🗂️ 瓦片信息:")
        try:
            cursor.execute("SELECT COUNT(*) FROM tiles")
            tile_count = cursor.fetchone()[0]
            print(f"  瓦片总数: {tile_count}")
            
            if tile_count > 0:
                cursor.execute("SELECT MIN(zoom_level), MAX(zoom_level) FROM tiles")
                min_zoom, max_zoom = cursor.fetchone()
                print(f"  缩放级别: {min_zoom} - {max_zoom}")
                
                # 检查第一个瓦片
                cursor.execute("SELECT zoom_level, tile_column, tile_row, LENGTH(tile_data) FROM tiles LIMIT 1")
                first_tile = cursor.fetchone()
                if first_tile:
                    z, x, y, size = first_tile
                    print(f"  第一个瓦片: z={z}, x={x}, y={y}, 大小={size}字节")
        except sqlite3.OperationalError:
            print("  ❌ 没有tiles表")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    mbtiles_path = project_root / 'web' / 'data' / 'flood_combined.mbtiles'
    
    print("MBTiles文件检查工具")
    print("=" * 40)
    
    if check_mbtiles(str(mbtiles_path)):
        print("\n✅ MBTiles文件检查完成")
    else:
        print("\n❌ MBTiles文件有问题")

if __name__ == "__main__":
    main()
