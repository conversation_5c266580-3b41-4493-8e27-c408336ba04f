#!/usr/bin/env python3
"""
坐标转换脚本
将投影坐标转换为地理坐标
"""

import json
import geopandas as gpd
from pathlib import Path
from shapely.geometry import shape

def convert_geojson_coordinates(input_file, output_file, source_crs='EPSG:4545'):
    """
    转换GeoJSON文件中的坐标系
    
    Args:
        input_file: 输入GeoJSON文件路径
        output_file: 输出GeoJSON文件路径
        source_crs: 源坐标系，默认EPSG:4545
    """
    print(f"正在转换坐标系: {input_file} -> {output_file}")
    print(f"源坐标系: {source_crs}")
    
    try:
        # 读取GeoJSON文件
        print("读取GeoJSON文件...")
        with open(input_file, 'r', encoding='utf-8') as f:
            geojson_data = json.load(f)
        
        print(f"特征数量: {len(geojson_data.get('features', []))}")
        
        # 转换为GeoDataFrame
        print("转换为GeoDataFrame...")
        gdf = gpd.GeoDataFrame.from_features(geojson_data['features'])
        
        # 设置源坐标系
        gdf.crs = source_crs
        print(f"设置源坐标系: {gdf.crs}")
        
        # 检查原始坐标范围
        bounds = gdf.total_bounds
        print(f"原始坐标范围: X[{bounds[0]:.2f}, {bounds[2]:.2f}], Y[{bounds[1]:.2f}, {bounds[3]:.2f}]")
        
        # 转换为WGS84
        print("转换到WGS84...")
        gdf_wgs84 = gdf.to_crs('EPSG:4326')
        
        # 检查转换后的坐标范围
        bounds_wgs84 = gdf_wgs84.total_bounds
        print(f"转换后坐标范围: 经度[{bounds_wgs84[0]:.6f}, {bounds_wgs84[2]:.6f}], 纬度[{bounds_wgs84[1]:.6f}, {bounds_wgs84[3]:.6f}]")
        
        # 验证坐标有效性
        if not (-180 <= bounds_wgs84[0] <= 180 and -180 <= bounds_wgs84[2] <= 180):
            raise ValueError(f"经度超出有效范围: [{bounds_wgs84[0]:.6f}, {bounds_wgs84[2]:.6f}]")
        
        if not (-90 <= bounds_wgs84[1] <= 90 and -90 <= bounds_wgs84[3] <= 90):
            raise ValueError(f"纬度超出有效范围: [{bounds_wgs84[1]:.6f}, {bounds_wgs84[3]:.6f}]")
        
        print("坐标验证通过")
        
        # 转换回GeoJSON格式
        print("转换回GeoJSON格式...")
        converted_geojson = {
            "type": "FeatureCollection",
            "properties": geojson_data.get("properties", {}),
            "features": []
        }
        
        for idx, row in gdf_wgs84.iterrows():
            feature = {
                "type": "Feature",
                "properties": row.drop('geometry').to_dict(),
                "geometry": row.geometry.__geo_interface__
            }
            converted_geojson["features"].append(feature)
        
        # 保存转换后的文件
        print(f"保存转换后的文件: {output_file}")
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_geojson, f, ensure_ascii=False, indent=2)
        
        print("坐标转换完成!")
        return True
        
    except Exception as e:
        print(f"坐标转换失败: {e}")
        return False

def main():
    """主函数"""
    # 设置文件路径
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    input_file = project_root / "database" / "flood_combined.geojson"
    output_file = project_root / "web" / "data" / "flood_combined.geojson"
    
    print("内涝数据坐标转换工具")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    # 检查输入文件是否存在
    if not input_file.exists():
        print(f"错误: 输入文件不存在: {input_file}")
        print("请先运行 data_processor.py 生成数据文件")
        return
    
    # 执行坐标转换
    success = convert_geojson_coordinates(
        str(input_file), 
        str(output_file), 
        source_crs='EPSG:4545'
    )
    
    if success:
        print("\n转换成功! Web应用现在可以正确显示地理位置了。")
        
        # 同时复制统计文件
        stats_input = project_root / "database" / "statistics.json"
        stats_output = project_root / "web" / "data" / "statistics.json"
        
        if stats_input.exists():
            import shutil
            shutil.copy2(stats_input, stats_output)
            print(f"统计文件已复制: {stats_output}")
    else:
        print("\n转换失败，请检查错误信息")

if __name__ == "__main__":
    main()
