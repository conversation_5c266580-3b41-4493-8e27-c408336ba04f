#!/usr/bin/env python3
"""
测试瓦片服务器的简单脚本
"""

import requests
import sys
from pathlib import Path

def test_tile_server(base_url="http://localhost:8080"):
    """测试瓦片服务器"""
    print(f"测试瓦片服务器: {base_url}")
    
    # 测试根路径
    try:
        print("\n1. 测试根路径...")
        response = requests.get(base_url)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        if response.status_code == 200:
            print(f"响应内容: {response.text}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"根路径测试失败: {e}")
    
    # 测试元数据端点
    try:
        print("\n2. 测试元数据端点...")
        response = requests.get(f"{base_url}/metadata")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        if response.status_code == 200:
            print(f"元数据: {response.json()}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"元数据测试失败: {e}")
    
    # 测试瓦片端点
    try:
        print("\n3. 测试瓦片端点...")
        response = requests.get(f"{base_url}/tiles/0/0/0.pbf")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        if response.status_code == 200:
            print(f"瓦片数据大小: {len(response.content)} 字节")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"瓦片测试失败: {e}")

def check_mbtiles_file():
    """检查MBTiles文件"""
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    mbtiles_path = project_root / 'web' / 'data' / 'flood_combined.mbtiles'
    
    print(f"检查MBTiles文件: {mbtiles_path}")
    
    if not mbtiles_path.exists():
        print("❌ MBTiles文件不存在")
        return False
    
    print(f"✅ MBTiles文件存在，大小: {mbtiles_path.stat().st_size / 1024 / 1024:.2f} MB")
    
    # 尝试读取MBTiles文件
    try:
        import sqlite3
        conn = sqlite3.connect(str(mbtiles_path))
        cursor = conn.cursor()
        
        # 检查元数据
        cursor.execute("SELECT name, value FROM metadata")
        metadata = dict(cursor.fetchall())
        print(f"元数据: {metadata}")
        
        # 检查瓦片数量
        cursor.execute("SELECT COUNT(*) FROM tiles")
        tile_count = cursor.fetchone()[0]
        print(f"瓦片数量: {tile_count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 读取MBTiles文件失败: {e}")
        return False

def main():
    """主函数"""
    print("瓦片服务器测试工具")
    print("=" * 50)
    
    # 检查MBTiles文件
    if not check_mbtiles_file():
        print("\n请确保MBTiles文件存在并且有效")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    
    # 测试服务器
    test_tile_server()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
