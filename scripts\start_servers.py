#!/usr/bin/env python3
"""
启动Web服务器和瓦片服务器的脚本
"""

import subprocess
import sys
import time
import os
import signal
from pathlib import Path
import threading

def start_web_server(port=8001, directory=None):
    """启动Web服务器"""
    if directory is None:
        script_dir = Path(__file__).parent
        directory = script_dir.parent / 'web'
    
    print(f"启动Web服务器...")
    print(f"目录: {directory}")
    print(f"端口: {port}")
    
    try:
        # 使用Python内置的HTTP服务器
        process = subprocess.Popen([
            sys.executable, '-m', 'http.server', str(port)
        ], cwd=directory, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        return process
    except Exception as e:
        print(f"启动Web服务器失败: {e}")
        return None

def start_tile_server(mbtiles_path, port=8080, host='localhost'):
    """启动瓦片服务器"""
    script_dir = Path(__file__).parent
    tile_server_script = script_dir / 'tile_server.py'
    
    print(f"启动瓦片服务器...")
    print(f"MBTiles文件: {mbtiles_path}")
    print(f"端口: {port}")
    
    try:
        process = subprocess.Popen([
            sys.executable, str(tile_server_script),
            str(mbtiles_path), '--port', str(port), '--host', host
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        return process
    except Exception as e:
        print(f"启动瓦片服务器失败: {e}")
        return None

def monitor_process(process, name):
    """监控进程输出"""
    def read_output():
        for line in iter(process.stdout.readline, b''):
            print(f"[{name}] {line.decode().strip()}")
        
        for line in iter(process.stderr.readline, b''):
            print(f"[{name} ERROR] {line.decode().strip()}")
    
    thread = threading.Thread(target=read_output, daemon=True)
    thread.start()
    return thread

def main():
    """主函数"""
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    
    # 查找MBTiles文件
    mbtiles_path = project_root / 'web' / 'data' / 'flood_combined.mbtiles'
    
    if not mbtiles_path.exists():
        print(f"警告: MBTiles文件不存在: {mbtiles_path}")
        print("将只启动Web服务器，使用GeoJSON数据")
        mbtiles_path = None
    
    processes = []
    
    try:
        # 启动Web服务器
        web_process = start_web_server(8001, project_root / 'web')
        if web_process:
            processes.append(('Web服务器', web_process))
            monitor_process(web_process, 'Web')
            time.sleep(1)  # 等待启动
        
        # 启动瓦片服务器（如果有MBTiles文件）
        if mbtiles_path:
            tile_process = start_tile_server(mbtiles_path, 8080)
            if tile_process:
                processes.append(('瓦片服务器', tile_process))
                monitor_process(tile_process, 'Tiles')
                time.sleep(2)  # 等待启动
        
        if not processes:
            print("没有成功启动任何服务器")
            return
        
        print("\n" + "="*50)
        print("服务器启动完成!")
        print("="*50)
        print(f"Web应用地址: http://localhost:8001")
        if mbtiles_path:
            print(f"瓦片服务器: http://localhost:8080")
        print("按 Ctrl+C 停止所有服务器")
        print("="*50)
        
        # 等待用户中断
        try:
            while True:
                # 检查进程是否还在运行
                for name, process in processes:
                    if process.poll() is not None:
                        print(f"\n{name} 已停止")
                        return
                
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
    
    finally:
        # 停止所有进程
        for name, process in processes:
            try:
                print(f"停止 {name}...")
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    print(f"强制停止 {name}...")
                    process.kill()
                    process.wait()
                    
            except Exception as e:
                print(f"停止 {name} 时出错: {e}")
        
        print("所有服务器已停止")

if __name__ == "__main__":
    main()
