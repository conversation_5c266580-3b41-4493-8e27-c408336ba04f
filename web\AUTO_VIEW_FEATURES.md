# 自动视图调整功能说明

## 功能概述

内涝积水动态演示系统现在具备智能视图调整功能，能够自动将地图视角定位到内涝数据的实际地理位置。

## 主要特性

### 1. 自动数据边界计算
- 系统会自动分析所有内涝网格的地理坐标
- 计算数据的最小外接矩形边界
- 确定数据的地理中心点

### 2. 智能视图调整
- **数据加载时**：数据加载完成后自动调整地图视图
- **播放开始时**：点击播放按钮时自动定位到数据范围
- **平滑动画**：使用2秒平滑动画过渡到目标视图

### 3. 多种地图支持
- **Mapbox地图**：使用`fitBounds`API精确调整视图
- **简化地图**：使用地理坐标布局显示数据分布
- **自适应布局**：根据数据密度自动选择最佳显示方式

### 4. 视觉反馈
- 显示"正在调整视图到数据范围..."通知
- 通知会在2.5秒后自动消失
- 提供清晰的用户体验反馈

## 技术实现

### 边界计算算法
```javascript
// 遍历所有多边形特征
features.forEach(feature => {
    if (feature.geometry.type === 'Polygon') {
        const coordinates = feature.geometry.coordinates[0];
        coordinates.forEach(coord => {
            const [lng, lat] = coord;
            minLng = Math.min(minLng, lng);
            maxLng = Math.max(maxLng, lng);
            minLat = Math.min(minLat, lat);
            maxLat = Math.max(maxLat, lat);
        });
    }
});
```

### 视图调整参数
- **边距**：50像素边距确保数据不贴边显示
- **最大缩放**：限制在15级避免过度放大
- **动画时长**：2秒平滑过渡动画
- **延迟调整**：数据加载后延迟500ms确保地图完全准备

### 地理坐标布局
对于简化地图模式，系统会：
1. 计算地理坐标到屏幕坐标的缩放比例
2. 保持地理位置的相对关系
3. 确保所有网格都在可视范围内

## 使用场景

### 场景1：首次加载
1. 用户打开页面
2. 系统加载内涝数据
3. 自动计算数据边界
4. 调整地图视图到数据范围
5. 显示调整通知

### 场景2：开始播放
1. 用户点击播放按钮
2. 系统检查当前视图
3. 自动调整到最佳观看角度
4. 开始播放内涝动画

### 场景3：数据切换
1. 加载新的内涝数据集
2. 重新计算边界
3. 平滑过渡到新的视图范围

## 配置选项

### 调整参数
- `padding`: 边界边距（默认50px）
- `maxZoom`: 最大缩放级别（默认15）
- `duration`: 动画时长（默认2000ms）

### 通知样式
- 位置：屏幕顶部居中
- 样式：蓝色半透明背景
- 动画：淡入淡出效果

## 兼容性

- ✅ Mapbox GL JS v2.15+
- ✅ 现代浏览器（Chrome, Firefox, Safari, Edge）
- ✅ 移动设备浏览器
- ✅ 简化地图模式（无Mapbox Token时）

## 故障排除

### 问题1：视图调整不生效
**原因**：数据边界计算失败
**解决**：检查GeoJSON数据格式，确保包含有效的Polygon几何

### 问题2：调整过度放大
**原因**：数据范围太小
**解决**：调整`maxZoom`参数限制最大缩放级别

### 问题3：动画卡顿
**原因**：数据量过大或设备性能限制
**解决**：减少`duration`参数或禁用动画

## 开发者接口

### 手动调整视图
```javascript
// 手动触发视图调整
floodApp.fitMapToData();

// 设置自定义边界
floodApp.dataBounds = {
    southwest: [lng1, lat1],
    northeast: [lng2, lat2],
    center: [centerLng, centerLat]
};
```

### 监听调整事件
```javascript
// 监听播放开始事件
floodApp.timeController.onPlayStart(() => {
    console.log('播放开始，视图已调整');
});
```

这个功能大大提升了用户体验，确保用户始终能看到完整的内涝数据分布，无需手动调整地图位置。
