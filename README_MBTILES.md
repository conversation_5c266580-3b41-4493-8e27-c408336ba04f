# 矢量瓦片支持说明

## 概述

Web应用现在支持两种数据加载方式：
1. **GeoJSON模式**（默认）- 直接加载GeoJSON文件
2. **矢量瓦片模式** - 使用MBTiles矢量瓦片，性能更好

## 矢量瓦片模式使用方法

### 1. 准备MBTiles文件

确保你已经将GeoJSON文件转换为MBTiles格式，并放置在：
```
web/data/flood_combined.mbtiles
```

### 2. 启动服务器

使用提供的启动脚本：

```bash
# 启动Web服务器和瓦片服务器
python scripts/start_servers.py
```

这将启动：
- Web服务器：http://localhost:8001
- 瓦片服务器：http://localhost:8080

### 3. 手动启动（可选）

如果需要手动启动服务器：

```bash
# 启动瓦片服务器
python scripts/tile_server.py web/data/flood_combined.mbtiles --port 8080

# 在另一个终端启动Web服务器
cd web
python -m http.server 8001
```

## 配置说明

在 `web/js/app.js` 中的配置：

```javascript
this.config = {
    // ...
    useMBTiles: true,  // 启用矢量瓦片模式
    tileServerUrl: 'http://localhost:8080/tiles/{z}/{x}/{y}.pbf'
};
```

## 自动回退机制

如果瓦片服务器不可用，应用会自动回退到GeoJSON模式：

1. 检测瓦片服务器是否响应
2. 如果失败，自动切换到GeoJSON模式
3. 在控制台显示相应的警告信息

## 性能优势

矢量瓦片模式的优势：
- **更快的加载速度** - 按需加载瓦片
- **更好的缩放性能** - 支持大数据集
- **减少内存使用** - 只加载可见区域的数据
- **流畅的动画** - 瓦片缓存提高时间变化性能

## 故障排除

### 瓦片服务器无法启动

1. 检查MBTiles文件是否存在
2. 确保端口8080未被占用
3. 查看控制台错误信息

### 地图显示空白

1. 检查浏览器控制台的网络错误
2. 确认瓦片服务器正在运行
3. 验证瓦片URL格式是否正确

### 时间动画不流畅

1. 确认MBTiles文件包含所有时间点的数据
2. 检查瓦片元数据配置
3. 考虑调整动画播放速度

## 开发说明

### 瓦片服务器API

- `GET /tiles/{z}/{x}/{y}.pbf` - 获取矢量瓦片
- `GET /metadata` - 获取MBTiles元数据
- 支持CORS跨域请求

### 数据结构要求

MBTiles文件应包含：
- 矢量图层名称：`flood_data`
- 时间字段：每个时间点作为单独的属性
- 网格标识：`cell_name`字段

### 自定义配置

可以修改以下配置：
- 瓦片服务器端口
- 瓦片URL模板
- 缩放级别范围
- 图层样式配置
