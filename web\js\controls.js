/**
 * 时间控制器类
 * 负责管理时间轴播放、暂停、速度控制等功能
 */
class TimeController {
    constructor() {
        this.timePoints = [];
        this.currentIndex = 0;
        this.isPlaying = false;
        this.playSpeed = 1.0;
        this.playInterval = null;
        this.onTimeChangeCallback = null;
        this.onPlayStartCallback = null;

        this.initializeElements();
        this.bindEvents();
    }
    
    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        this.elements = {
            currentTime: document.getElementById('current-time'),
            playPauseBtn: document.getElementById('play-pause-btn'),
            resetBtn: document.getElementById('reset-btn'),
            stepBackBtn: document.getElementById('step-back-btn'),
            stepForwardBtn: document.getElementById('step-forward-btn'),
            timeSlider: document.getElementById('time-slider'),
            speedSlider: document.getElementById('speed-slider'),
            speedDisplay: document.getElementById('speed-display'),
            playIcon: document.querySelector('.play-icon'),
            pauseIcon: document.querySelector('.pause-icon'),
            timeStart: document.querySelector('.time-start'),
            timeEnd: document.querySelector('.time-end')
        };
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 播放/暂停按钮
        this.elements.playPauseBtn.addEventListener('click', () => {
            this.togglePlayPause();
        });
        
        // 重置按钮
        this.elements.resetBtn.addEventListener('click', () => {
            this.reset();
        });
        
        // 后退按钮
        this.elements.stepBackBtn.addEventListener('click', () => {
            this.stepBack();
        });
        
        // 前进按钮
        this.elements.stepForwardBtn.addEventListener('click', () => {
            this.stepForward();
        });
        
        // 时间滑块
        this.elements.timeSlider.addEventListener('input', (e) => {
            const index = parseInt(e.target.value);
            this.setCurrentIndex(index);
        });
        
        // 速度滑块
        this.elements.speedSlider.addEventListener('input', (e) => {
            this.setPlaySpeed(parseFloat(e.target.value));
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboard(e);
        });
    }
    
    /**
     * 设置时间点数组
     */
    setTimePoints(timePoints) {
        this.timePoints = timePoints;
        this.currentIndex = 0;
        
        // 更新滑块范围
        this.elements.timeSlider.max = Math.max(0, timePoints.length - 1);
        this.elements.timeSlider.value = 0;
        
        // 更新时间标签
        if (timePoints.length > 0) {
            this.elements.timeStart.textContent = timePoints[0];
            this.elements.timeEnd.textContent = timePoints[timePoints.length - 1];
        }
        
        this.updateDisplay();
    }
    
    /**
     * 设置当前时间索引
     */
    setCurrentIndex(index) {
        if (index >= 0 && index < this.timePoints.length) {
            this.currentIndex = index;
            this.elements.timeSlider.value = index;
            this.updateDisplay();
            this.notifyTimeChange();
        }
    }
    
    /**
     * 获取当前时间点
     */
    getCurrentTime() {
        return this.timePoints[this.currentIndex] || null;
    }
    
    /**
     * 获取当前索引
     */
    getCurrentIndex() {
        return this.currentIndex;
    }
    
    /**
     * 切换播放/暂停状态
     */
    togglePlayPause() {
        if (this.isPlaying) {
            this.pause();
        } else {
            this.play();
        }
    }
    
    /**
     * 开始播放
     */
    play() {
        if (this.timePoints.length === 0) return;

        this.isPlaying = true;
        this.updatePlayPauseButton();

        // 通知开始播放（用于自动调整视图）
        if (this.onPlayStartCallback) {
            this.onPlayStartCallback();
        }

        const interval = 1000 / this.playSpeed; // 基础间隔1秒
        this.playInterval = setInterval(() => {
            if (this.currentIndex < this.timePoints.length - 1) {
                this.setCurrentIndex(this.currentIndex + 1);
            } else {
                // 播放完毕，重新开始或停止
                this.reset();
                // this.pause(); // 如果希望播放完毕后停止，取消注释这行
            }
        }, interval);
    }
    
    /**
     * 暂停播放
     */
    pause() {
        this.isPlaying = false;
        this.updatePlayPauseButton();
        
        if (this.playInterval) {
            clearInterval(this.playInterval);
            this.playInterval = null;
        }
    }
    
    /**
     * 重置到开始
     */
    reset() {
        this.pause();
        this.setCurrentIndex(0);
    }
    
    /**
     * 后退一步
     */
    stepBack() {
        if (this.currentIndex > 0) {
            this.setCurrentIndex(this.currentIndex - 1);
        }
    }
    
    /**
     * 前进一步
     */
    stepForward() {
        if (this.currentIndex < this.timePoints.length - 1) {
            this.setCurrentIndex(this.currentIndex + 1);
        }
    }
    
    /**
     * 设置播放速度
     */
    setPlaySpeed(speed) {
        this.playSpeed = speed;
        this.elements.speedDisplay.textContent = speed + 'x';
        
        // 如果正在播放，重新启动以应用新速度
        if (this.isPlaying) {
            this.pause();
            this.play();
        }
    }
    
    /**
     * 更新播放/暂停按钮显示
     */
    updatePlayPauseButton() {
        if (this.isPlaying) {
            this.elements.playIcon.style.display = 'none';
            this.elements.pauseIcon.style.display = 'inline';
        } else {
            this.elements.playIcon.style.display = 'inline';
            this.elements.pauseIcon.style.display = 'none';
        }
    }
    
    /**
     * 更新显示
     */
    updateDisplay() {
        const currentTime = this.getCurrentTime();
        this.elements.currentTime.textContent = currentTime || '--';
    }
    
    /**
     * 处理键盘快捷键
     */
    handleKeyboard(e) {
        switch(e.code) {
            case 'Space':
                e.preventDefault();
                this.togglePlayPause();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                this.stepBack();
                break;
            case 'ArrowRight':
                e.preventDefault();
                this.stepForward();
                break;
            case 'Home':
                e.preventDefault();
                this.reset();
                break;
        }
    }
    
    /**
     * 设置时间变化回调函数
     */
    onTimeChange(callback) {
        this.onTimeChangeCallback = callback;
    }

    /**
     * 设置播放开始回调函数
     */
    onPlayStart(callback) {
        this.onPlayStartCallback = callback;
    }
    
    /**
     * 通知时间变化
     */
    notifyTimeChange() {
        if (this.onTimeChangeCallback) {
            this.onTimeChangeCallback(this.getCurrentTime(), this.getCurrentIndex());
        }
    }
    
    /**
     * 销毁控制器
     */
    destroy() {
        this.pause();
        // 移除事件监听器等清理工作
    }
}
